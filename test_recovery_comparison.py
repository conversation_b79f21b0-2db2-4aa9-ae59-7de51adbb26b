#!/usr/bin/env python3
"""
Test script to compare recovery strategies:
1. Current: Single full recovery
2. New: Layer-by-layer gradual recovery
"""

def test_recovery_strategies():
    print("🔄 RECOVERY STRATEGY COMPARISON")
    print("=" * 60)
    
    qty_2 = 248  # Risk management amount
    
    print("\n📊 Scenario: Position went through all 4 levels")
    print("   • Level 1 (-5%): Reduced by 248")
    print("   • Level 2 (-10%): Zone notification")  
    print("   • Level 3 (-15%): Zone notification")
    print("   • Level 4 (-20%): Emergency exit (position closed)")
    print("   • Current position: 0")
    print("   • All flags: L1=True, L2=True, L3=True, L4=True")
    
    print("\n" + "="*60)
    print("STRATEGY 1: CURRENT (Single Full Recovery)")
    print("="*60)
    
    print("\n🔄 Recovery Scenario:")
    recovery_scenarios = [
        (-16, "ROE improves to -16%"),
        (-11, "ROE improves to -11%"), 
        (-6, "ROE improves to -6%"),
        (1, "ROE improves to +1%")
    ]
    
    for roe, description in recovery_scenarios:
        print(f"\n📈 {description}")
        
        # Current strategy logic
        recovery_thresholds = [(4, -15), (3, -10), (2, -5), (1, 0)]
        for layer, threshold in recovery_thresholds:
            if roe > threshold:
                print(f"   ✅ FULL RECOVERY triggered at Level {layer}")
                print(f"   📈 Restore: {qty_2} units")
                print(f"   🗑️  Remove: ALL flags (L1, L2, L3, L4)")
                print(f"   📊 Result: Full position restored in ONE action")
                break
        else:
            print(f"   ❌ No recovery (ROE {roe}% not sufficient)")
    
    print("\n" + "="*60)
    print("STRATEGY 2: NEW (Layer-by-Layer Gradual Recovery)")
    print("="*60)
    
    print("\n🔄 Recovery Scenario:")
    current_flags = [True, True, True, True]  # L1, L2, L3, L4
    current_position = 0
    
    for roe, description in recovery_scenarios:
        print(f"\n📈 {description}")
        
        # New strategy logic
        recovery_actions = [
            (4, -15, "Level 4 → Level 3"),
            (3, -10, "Level 3 → Level 2"), 
            (2, -5, "Level 2 → Level 1"),
            (1, 0, "Level 1 → Full Recovery")
        ]
        
        action_taken = False
        for layer, threshold, desc in recovery_actions:
            if current_flags[layer-1] and roe > threshold:
                if layer == 1:
                    recovery_qty = qty_2  # Full restoration from Level 1
                    print(f"   ✅ {desc}")
                    print(f"   📈 Restore: {recovery_qty} units (FULL)")
                    print(f"   🗑️  Remove: Level {layer} flag only")
                    current_flags[layer-1] = False
                    current_position += recovery_qty
                else:
                    recovery_qty = qty_2 * 0.25  # Partial restoration
                    print(f"   ✅ {desc}")
                    print(f"   📈 Restore: {recovery_qty} units (PARTIAL)")
                    print(f"   🗑️  Remove: Level {layer} flag only")
                    current_flags[layer-1] = False
                    current_position += recovery_qty
                
                flag_status = [f"L{i+1}={current_flags[i]}" for i in range(4)]
                print(f"   🏴 Flags: {', '.join(flag_status)}")
                print(f"   📊 Position: {current_position}")
                action_taken = True
                break
        
        if not action_taken:
            print(f"   ❌ No recovery (ROE {roe}% not sufficient)")
    
    print("\n" + "="*60)
    print("📊 COMPARISON SUMMARY")
    print("="*60)
    
    print("\n🔄 STRATEGY 1 (Current - Single Recovery):")
    print("   ✅ Pros:")
    print("      • Simple and clean")
    print("      • Fast full recovery when market improves")
    print("      • Prevents over-trading during recovery")
    print("      • Less complexity")
    print("   ⚠️  Cons:")
    print("      • All-or-nothing approach")
    print("      • Might miss gradual recovery opportunities")
    
    print("\n🔄 STRATEGY 2 (New - Layer-by-Layer):")
    print("   ✅ Pros:")
    print("      • Gradual position building")
    print("      • More responsive to market improvements")
    print("      • Better risk distribution")
    print("      • Allows partial recovery at each level")
    print("   ⚠️  Cons:")
    print("      • More complex logic")
    print("      • Multiple transactions during recovery")
    print("      • Potential for more frequent trading")
    
    print("\n🎯 RECOMMENDATION:")
    print("   For your trading style, STRATEGY 2 (Layer-by-Layer) is better because:")
    print("   • You want to increase size at every layer recovery")
    print("   • More responsive to market changes")
    print("   • Better position management")
    print("   • Aligns with your 4-level risk system")

if __name__ == "__main__":
    test_recovery_strategies()
