# Risk Management
End = 12
Start = 0
if (long == 1 or short == 1) and ROE < 15:
    print(f"{formatted_time} - [SYSTEM] Risk Management Mode")
    while Start < End:

        exchange.load_markets()
        positions = exchange.fetch_positions(symbols=[symbol])

        long = 0
        short = 0

        for i in positions:
            side = i['side']
            position_amt = float(i["info"]['positionAmt'])
            tradeqty = abs(position_amt)
            entryPrice = i["info"]['entryPrice']
            Pnl = i["info"]['unRealizedProfit']
            ROE = round(float(i['percentage']), 2)

            if side == 'long':
                long = 1
            elif side == 'short':
                short = 1

        # ------------------------------
        # Layer Files Dictionary
        # ------------------------------
        level_files = {
            1: f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt",
            2: f"/root/02_implementation/03_app_logs/{Coin}_risk_level_2.txt",
            3: f"/root/02_implementation/03_app_logs/{Coin}_risk_level_3.txt",
            4: f"/root/02_implementation/03_app_logs/{Coin}_risk_level_4.txt"
        }

        # ------------------------------
        # Remove lower layers function
        # ------------------------------
        def remove_lower_layers(current_layer):
            for l in range(1, current_layer):
                f = level_files[l]
                if os.path.exists(f):
                    os.remove(f)

        # ------------------------------
        # LONG Positions
        # ------------------------------
        if long == 1:
            # Layer 1
            if ROE < -5 and not os.path.exists(level_files[1]):
                binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=qty_2)
                msg = f"⚠️ *{Coin} LONG SIZE REDUCED*\nLevel 1 activated\nROE: {ROE:.2f}%\nEntry: {entryPrice}\nPnL: {float(Pnl):.2f}"
                requests.get(f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={msg}&parse_mode=Markdown')
                open(level_files[1], 'w').close()

            # Layer 2
            if ROE < -10 and not os.path.exists(level_files[2]):
                remove_lower_layers(2)
                open(level_files[2], 'w').close()
                msg = f"⚠️ *{Coin} LONG ENTERED LEVEL 2 ZONE*\nROE: {ROE:.2f}%\nEntry: {entryPrice}\nPnL: {float(Pnl):.2f}"
                requests.get(f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={msg}&parse_mode=Markdown')

            # Layer 3
            if ROE < -15 and not os.path.exists(level_files[3]):
                remove_lower_layers(3)
                open(level_files[3], 'w').close()
                msg = f"⚠️ *{Coin} LONG ENTERED LEVEL 3 ZONE*\nROE: {ROE:.2f}%\nEntry: {entryPrice}\nPnL: {float(Pnl):.2f}"
                requests.get(f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={msg}&parse_mode=Markdown')

            # Layer 4 - Emergency Exit
            if ROE < -20 and not os.path.exists(level_files[4]):
                # Close remaining position at -20% ROE
                binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=tradeqty)
                remove_lower_layers(4)
                open(level_files[4], 'w').close()
                msg = f"🛑 *{Coin} LONG EMERGENCY EXIT*\nROE: {ROE:.2f}%\nEntry: {entryPrice}\nPnL: {float(Pnl):.2f}\nPosition CLOSED"
                requests.get(f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={msg}&parse_mode=Markdown')

            # Recovery (SINGLE restoration when any reversal condition is met)
            recovery_thresholds = [(4, -15), (3, -10), (2, -5), (1, 0)]

            for layer, threshold in recovery_thresholds:
                if os.path.exists(level_files[layer]) and ROE > threshold:
                    # Single recovery - restore full qty_2 amount
                    recovery_qty = qty_2

                    binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=recovery_qty)
                    msg = f"✅ *{Coin} LONG RECOVERY*\nROE: {ROE:.2f}%\nRestored: {recovery_qty}\nTrigger: Level {layer} reversal\nEntry: {entryPrice}"
                    requests.get(f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={msg}&parse_mode=Markdown')

                    # Remove ALL flags after single recovery
                    for f in level_files.values():
                        if os.path.exists(f):
                            os.remove(f)
                    break  # Only ONE recovery total

        # ------------------------------
        # SHORT Positions
        # ------------------------------
        if short == 1:
            # Layer 1
            if ROE < -5 and not os.path.exists(level_files[1]):
                binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=qty_2)
                msg = f"⚠️ *{Coin} SHORT SIZE REDUCED*\nLevel 1 activated\nROE: {ROE:.2f}%\nEntry: {entryPrice}\nPnL: {float(Pnl):.2f}"
                requests.get(f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={msg}&parse_mode=Markdown')
                open(level_files[1], 'w').close()

            # Layer 2
            if ROE < -10 and not os.path.exists(level_files[2]):
                remove_lower_layers(2)
                open(level_files[2], 'w').close()
                msg = f"⚠️ *{Coin} SHORT ENTERED LEVEL 2 ZONE*\nROE: {ROE:.2f}%\nEntry: {entryPrice}\nPnL: {float(Pnl):.2f}"
                requests.get(f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={msg}&parse_mode=Markdown')

            # Layer 3
            if ROE < -15 and not os.path.exists(level_files[3]):
                remove_lower_layers(3)
                open(level_files[3], 'w').close()
                msg = f"⚠️ *{Coin} SHORT ENTERED LEVEL 3 ZONE*\nROE: {ROE:.2f}%\nEntry: {entryPrice}\nPnL: {float(Pnl):.2f}"
                requests.get(f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={msg}&parse_mode=Markdown')

            # Layer 4 - Emergency Exit
            if ROE < -20 and not os.path.exists(level_files[4]):
                # Close remaining position at -20% ROE
                binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=tradeqty)
                remove_lower_layers(4)
                open(level_files[4], 'w').close()
                msg = f"🛑 *{Coin} SHORT EMERGENCY EXIT*\nROE: {ROE:.2f}%\nEntry: {entryPrice}\nPnL: {float(Pnl):.2f}\nPosition CLOSED"
                requests.get(f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={msg}&parse_mode=Markdown')

            # Recovery (SINGLE restoration when any reversal condition is met)
            recovery_thresholds = [(4, -15), (3, -10), (2, -5), (1, 0)]

            for layer, threshold in recovery_thresholds:
                if os.path.exists(level_files[layer]) and ROE > threshold:
                    # Single recovery - restore full qty_2 amount
                    recovery_qty = qty_2

                    binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=recovery_qty)
                    msg = f"✅ *{Coin} SHORT RECOVERY*\nROE: {ROE:.2f}%\nRestored: {recovery_qty}\nTrigger: Level {layer} reversal\nEntry: {entryPrice}"
                    requests.get(f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={msg}&parse_mode=Markdown')

                    # Remove ALL flags after single recovery
                    for f in level_files.values():
                        if os.path.exists(f):
                            os.remove(f)
                    break  # Only ONE recovery total

        # Wait before next iteration
        time.sleep(10)
        Start += 1
