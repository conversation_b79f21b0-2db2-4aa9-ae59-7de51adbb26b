

trading_enabled = 1
leaverage = 10
Trade_amount_usdt_1 = 70
Trade_amount_usdt_2 = 8
Trade_amount_usdt_3 = 150
symbol="WIF/USDT"
Coin = 'WIFUSDT'
ID  =  '1_0'
ACC = 'Hadi'
TF = '3m'

def get_latest_price(token):
    url = f"https://api.binance.com/api/v3/ticker/price?symbol={token}"
    response = requests.get(url)
    data = response.json()
    if 'price' in data:
        return data['price']
    else:
        return None

if __name__ == "__main__":
    Token = Coin  # Example: Bitcoin to USDT
    price = get_latest_price(Token)

    
    if price is not None:
        # Compute Measure (keep as floats for calculations)
        tradeable_quantity_1 = (Trade_amount_usdt_1 / float(price)) * leaverage
        tradeable_quantity_2 = (Trade_amount_usdt_2 / float(price)) * leaverage
        tradeable_quantity_3 = (Trade_amount_usdt_3 / float(price)) * leaverage
        
        # Calculate the difference (keep as float)
        quantity_difference = tradeable_quantity_1 - tradeable_quantity_2

        # Format for calculations - keep as numbers for proper arithmetic
        Measure_1 = None
        Measure_2 = None
        Measure_3 = None

        if float(price) > 10:
            Measure_1 = round(tradeable_quantity_1, 2)
            Measure_2 = round(quantity_difference, 2)
            Measure_3 = round(tradeable_quantity_3, 2)
        elif float(price) < 10:
            # Keep as numbers (integers) for calculations
            Measure_1 = int(tradeable_quantity_1)
            Measure_2 = int(quantity_difference)
            Measure_3 = int(tradeable_quantity_3)
        elif float(price) == 10:
            # Handle exact price of 10
            Measure_1 = round(tradeable_quantity_1, 2)
            Measure_2 = round(quantity_difference, 2)
            Measure_3 = round(tradeable_quantity_3, 2)  # Fixed: was tradeable_quantity_1


        #print(f"-- The latest price of {Token} is: {price}\n-- With {Trade_amount_usdt_1} USDT Amount\n-- {leaverage}x leaverage \n-- you can trade: {Measure_1}")
        #print ("01 ---------------------------------------")

        #print(f"-- The latest price of {Token} is: {price}\n-- With {Trade_amount_usdt_1-Trade_amount_usdt_2} USDT Amount\n-- {leaverage}x leaverage \n-- you can trade: {Measure_2}")
        #print ("02 ---------------------------------------")
        
        #print(f"-- The latest price of {Token} is: {price}\n-- With {Trade_amount_usdt_3} USDT Amount\n-- {leaverage}x leaverage \n-- you can trade: {Measure_3}")
        #print ("03 ---------------------------------------")

        #print (Measure_2 + Measure_3 )
        #print (Trade_amount_usdt_1-Trade_amount_usdt_2)       
        #print (Measure_1-Measure_2) 

    else:
        print("Could not fetch the price. Please check the token and try again.")

qty = Measure_1
qty_2 = Measure_2
qty_3 = Measure_3



# Get the last row data frame and print column value
last_row = test_010.iloc[-1]
column_to_print_2 = 'Flag_012'
value = last_row[column_to_print_2]
print(f"{formatted_time} - [SYSTEM] Signal: {value}")

column_to_print_1 = 'P_PCT_012'
value = last_row[column_to_print_1]
print(f"{formatted_time} - [SYSTEM] ROE: {value}%")


ticker = binance_client.get_symbol_ticker(symbol=(Coin ))
price = ticker['price']

if trading_enabled == 1:

    long = 0
    short = 0

    for i in positions:
        symbol_name = i["info"]['symbol']
        side = i['side']
        tradeamount = i["info"]['isolatedMargin']
        position_amt = float(i["info"]['positionAmt'])
        tradeqty = abs(position_amt)
        entryPrice = i["info"]['entryPrice']
        markPrice = i["info"]['markPrice']
        Pnl = i["info"]['unRealizedProfit']
        #ROE = i['percentage']
        ROE = round(float(i['percentage']), 2)    

        if side == 'long':
            long = 1
            short = 2
        elif side == 'short':
            long = 2
            short = 1

    if (long == 1 or short == 1):
        print(f"{formatted_time} - [POSITION] ROE: {ROE}%")

        # Example if Pnl is a string like "$123.45" or "1,234.56"
        cleaned_Pnl = Pnl.replace(',', '').replace('$', '')
        Pnl_float = float(cleaned_Pnl)
        print(f"{formatted_time} - [POSITION] PNL: ${Pnl_float:.2f}")
                    
    # New Buy Position
    if (test_010['Buy_F_012'].iloc[-2] > 0) and (long == 0 or long == 1):
        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")

        # Remove new separate flag files
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long_1.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short_1.txt")


        border1 = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty)
        message = (f"🔵 *{Coin} LONG SIGNAL*\n\n"
                  f"📊 *Trade Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Direction: LONG\n\n"
                  f"Position successfully established. 📈")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)

        print(f"{formatted_time} - [TRADE] Long position opened successfully")
        time.sleep(2)

    # New Sell Position
    if (test_010['Sell_F_012'].iloc[-2] > 0) and (short == 0 or short == 1):
        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")

        # Remove new separate flag files
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long_1.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short_1.txt")

        sorder1 = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty)
        message = (f"🔴 *{Coin} SHORT SIGNAL*\n\n"
                  f"📊 *Trade Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Direction: SHORT\n\n"
                  f"Position successfully established. 📉")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)

        print(f"{formatted_time} - [TRADE] Short position opened successfully")
        time.sleep(2)

    # Close Long and Open Short
    if test_010['Sell_F_012'].iloc[-2] > 0 and long == 1:

        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt") 

        # Remove new separate flag files
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_long_1.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short.txt")
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short_1.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag_short_1.txt")

        # Close buy order
        sorder1 = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=tradeqty)
        message = (f"🔄 *{Coin} POSITION SWITCH*\n\n"
                  f"📊 *Trade Summary:*\n"
                  f"• Time: {current_time}\n"
                  f"• Long Position Closed: ${price}\n"
                  f"• Short Position Opened: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Closed ROE: {ROE:.2f}%\n"
                  f"• Realized PNL: ${float(Pnl):.2f}\n\n"
                  f"Position successfully switched from LONG to SHORT. 🔄")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - [TRADE] Long position closed for reversal")
        time.sleep(2)

        # Open Sell order
        border2 = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=qty)
        message = (f"🔴 *{Coin} SHORT SIGNAL*\n\n"
                  f"📊 *Trade Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Direction: SHORT\n\n"
                  f"New short position successfully established. 📉")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - [TRADE] Short position opened after reversal")

        # Reset position data after switch to prevent old ROE from triggering checkpoints
        exchange.load_markets()
        positions = exchange.fetch_positions(symbols=[symbol])

        # Reset position variables
        long = 0
        short = 0

        # Recalculate position data for the new SHORT position
        for i in positions:
            symbol_name = i["info"]['symbol']
            side = i['side']
            tradeamount = i["info"]['isolatedMargin']
            position_amt = float(i["info"]['positionAmt'])
            tradeqty = abs(position_amt)
            entryPrice = i["info"]['entryPrice']
            markPrice = i["info"]['markPrice']
            Pnl = i["info"]['unRealizedProfit']
            ROE = round(float(i['percentage']), 2)

            if side == 'long':
                long = 1
                short = 2
            elif side == 'short':
                long = 2
                short = 1

    # Close Short and Open Long
    if test_010['Buy_F_012'].iloc[-2] > 0 and short == 1:
        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt") 



        # Close Sell order
        bborder1 = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=tradeqty)
        message = (f"🔄 *{Coin} POSITION SWITCH*\n\n"
                  f"📊 *Trade Summary:*\n"
                  f"• Time: {current_time}\n"
                  f"• Short Position Closed: ${price}\n"
                  f"• Long Position Opened: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Closed ROE: {ROE:.2f}%\n"
                  f"• Realized PNL: ${float(Pnl):.2f}\n\n"
                  f"Position successfully switched from SHORT to LONG. 🔄")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - [TRADE] Short position closed for reversal")
        time.sleep(2)


        # Open Buy order
        bborder2 = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=qty)
        message = (f"🔵 *{Coin} LONG SIGNAL*\n\n"
                  f"📊 *Trade Details:*\n"
                  f"• Time: {current_time}\n"
                  f"• Entry Price: ${price}\n"
                  f"• Position Size: {qty}\n"
                  f"• Direction: LONG\n\n"
                  f"New long position successfully established. 📈")
        url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
        requests.get(url)
        print(f"{formatted_time} - [TRADE] Long position opened after reversal")

        # Reset position data after switch to prevent old ROE from triggering checkpoints
        exchange.load_markets()
        positions = exchange.fetch_positions(symbols=[symbol])

        # Reset position variables
        long = 0
        short = 0

        # Recalculate position data for the new LONG position
        for i in positions:
            symbol_name = i["info"]['symbol']
            side = i['side']
            tradeamount = i["info"]['isolatedMargin']
            position_amt = float(i["info"]['positionAmt'])
            tradeqty = abs(position_amt)
            entryPrice = i["info"]['entryPrice']
            markPrice = i["info"]['markPrice']
            Pnl = i["info"]['unRealizedProfit']
            ROE = round(float(i['percentage']), 2)

            if side == 'long':
                long = 1
                short = 2
            elif side == 'short':
                long = 2
                short = 1

    # Take Profit - Short Position
    if test_010['Buy_F_013'].iloc[-2] > 0 and short == 1:


        # Remove flag file if exists
        if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
            os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")



        try:
            stpclose = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=tradeqty)
            message = (f"🛑 *{Coin} SHORT POSITION CLOSED*\n\n"
                    f"📊 *Exit Summary:*\n"
                    f"• Exit Time: {current_time}\n"
                    f"• Entry Price: ${entryPrice}\n"
                    f"• Exit Price: ${price}\n"
                    f"• Position Size: {tradeqty}\n"
                    f"• Final ROE: {ROE:.2f}%\n"
                    f"• Realized PNL: ${float(Pnl):.2f}\n\n"
                    f"Stop-loss order executed successfully. �")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [STOP-LOSS] Short position closed successfully")
            time.sleep(2)


        except Exception as e:
            print(f"{formatted_time} - [ERROR] Short position stop-loss failed: {str(e)}")

    # Take Profit - Long Position
    if test_010['Sell_F_013'].iloc[-2] > 0 and long == 1:




        try:
            btpclose = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=tradeqty)
            message = (f"🛑 *{Coin} LONG POSITION CLOSED*\n\n"
                    f"📊 *Exit Summary:*\n"
                    f"• Exit Time: {current_time}\n"
                    f"• Entry Price: ${entryPrice}\n"
                    f"• Exit Price: ${price}\n"
                    f"• Position Size: {tradeqty}\n"
                    f"• Final ROE: {ROE:.2f}%\n"
                    f"• Realized PNL: ${float(Pnl):.2f}\n\n"
                    f"Stop-loss order executed successfully. �")
            url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
            requests.get(url)
            print(f"{formatted_time} - [STOP-LOSS] Long position closed successfully")
            time.sleep(2)

            # Remove flag file if exists
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt"):
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_tmp_flag.txt")


        except Exception as e:
            print(f"{formatted_time} - [ERROR] Long position stop-loss failed: {str(e)}")

# Enhanced Risk Management System
    # Simplified, efficient risk management with clear thresholds and minimal over-trading

    if (long == 1 or short == 1):
        print(f"{formatted_time} - [RISK-MGMT] Active - ROE: {ROE}%")

        # === SHORT POSITION RISK MANAGEMENT ===

        # Risk Level 1: Initial Risk Reduction at -4% ROE
        if short == 1 and ROE <= -4.0 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt"):

            # Determine reduction quantity based on size adjustment
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                reduction_qty = qty_3  # Reduce only the bonus amount first
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt")
            else:
                reduction_qty = qty_2 * 0.5  # Reduce 50% of base position

            try:
                reduce_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=reduction_qty)

                message = (f"⚠️ *{Coin} SHORT RISK LEVEL 1*\n\n"
                          f"📊 *Risk Management:*\n"
                          f"• ROE: {ROE:.2f}%\n"
                          f"• Reduced Size: {reduction_qty}\n"
                          f"• Trigger: -4% ROE\n\n"
                          f"Position size reduced to limit losses. 🛡️")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [RISK-MGMT] SHORT Level 1: Position reduced at {ROE}% ROE")

                # Create risk level flag
                open(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt", 'w').close()

            except Exception as e:
                print(f"{formatted_time} - [ERROR] SHORT Risk Level 1 failed: {str(e)}")

        # Risk Level 2: Emergency Stop at -8% ROE
        elif short == 1 and ROE <= -8.0 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_2.txt"):

            try:
                # Close remaining position
                close_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=tradeqty)

                message = (f"🛑 *{Coin} SHORT EMERGENCY STOP*\n\n"
                          f"📊 *Emergency Exit:*\n"
                          f"• Final ROE: {ROE:.2f}%\n"
                          f"• Realized PNL: ${float(Pnl):.2f}\n"
                          f"• Trigger: -8% ROE\n\n"
                          f"Position closed to prevent further losses. �")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [EMERGENCY] SHORT Position closed at {ROE}% ROE")

                # Clean up all risk flags
                for flag_file in [f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt",
                                f"/root/02_implementation/03_app_logs/{Coin}_risk_level_2.txt"]:
                    if os.path.exists(flag_file):
                        os.remove(flag_file)

            except Exception as e:
                print(f"{formatted_time} - [ERROR] SHORT Emergency stop failed: {str(e)}")

        # Recovery: Restore position when ROE improves to +2%
        elif short == 1 and ROE >= 2.0 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt"):

            try:
                # Restore 50% of reduced position
                restore_qty = qty_2 * 0.5
                restore_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=restore_qty)

                message = (f"✅ *{Coin} SHORT RECOVERY*\n\n"
                          f"📊 *Position Restored:*\n"
                          f"• ROE: {ROE:.2f}%\n"
                          f"• Restored Size: {restore_qty}\n"
                          f"• Trigger: +2% ROE\n\n"
                          f"Position size restored after recovery. 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [RECOVERY] SHORT Position restored at {ROE}% ROE")

                # Remove risk level 1 flag
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt")

            except Exception as e:
                print(f"{formatted_time} - [ERROR] SHORT Recovery failed: {str(e)}")


        # === LONG POSITION RISK MANAGEMENT ===

        # Risk Level 1: Initial Risk Reduction at -4% ROE
        if long == 1 and ROE <= -4.0 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt"):

            # Determine reduction quantity based on size adjustment
            if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):
                reduction_qty = qty_3  # Reduce only the bonus amount first
                os.remove(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt")
            else:
                reduction_qty = qty_2 * 0.5  # Reduce 50% of base position

            try:
                reduce_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=reduction_qty)

                message = (f"⚠️ *{Coin} LONG RISK LEVEL 1*\n\n"
                          f"📊 *Risk Management:*\n"
                          f"• ROE: {ROE:.2f}%\n"
                          f"• Reduced Size: {reduction_qty}\n"
                          f"• Trigger: -4% ROE\n\n"
                          f"Position size reduced to limit losses. 🛡️")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [RISK-MGMT] LONG Level 1: Position reduced at {ROE}% ROE")

                # Create risk level flag
                open(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt", 'w').close()

            except Exception as e:
                print(f"{formatted_time} - [ERROR] LONG Risk Level 1 failed: {str(e)}")

        # Risk Level 2: Emergency Stop at -8% ROE
        elif long == 1 and ROE <= -8.0 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_2.txt"):

            try:
                # Close remaining position
                close_order = binance_client.futures_create_order(symbol=(Coin), side='SELL', type='MARKET', quantity=tradeqty)

                message = (f"🛑 *{Coin} LONG EMERGENCY STOP*\n\n"
                          f"📊 *Emergency Exit:*\n"
                          f"• Final ROE: {ROE:.2f}%\n"
                          f"• Realized PNL: ${float(Pnl):.2f}\n"
                          f"• Trigger: -8% ROE\n\n"
                          f"Position closed to prevent further losses. �")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [EMERGENCY] LONG Position closed at {ROE}% ROE")

                # Clean up all risk flags
                for flag_file in [f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt",
                                f"/root/02_implementation/03_app_logs/{Coin}_risk_level_2.txt"]:
                    if os.path.exists(flag_file):
                        os.remove(flag_file)

            except Exception as e:
                print(f"{formatted_time} - [ERROR] LONG Emergency stop failed: {str(e)}")

        # Recovery: Restore position when ROE improves to +2%
        elif long == 1 and ROE >= 2.0 and os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt"):

            try:
                # Restore 50% of reduced position
                restore_qty = qty_2 * 0.5
                restore_order = binance_client.futures_create_order(symbol=(Coin), side='BUY', type='MARKET', quantity=restore_qty)

                message = (f"✅ *{Coin} LONG RECOVERY*\n\n"
                          f"📊 *Position Restored:*\n"
                          f"• ROE: {ROE:.2f}%\n"
                          f"• Restored Size: {restore_qty}\n"
                          f"• Trigger: +2% ROE\n\n"
                          f"Position size restored after recovery. 📈")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [RECOVERY] LONG Position restored at {ROE}% ROE")

                # Remove risk level 1 flag
                if os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt"):
                    os.remove(f"/root/02_implementation/03_app_logs/{Coin}_risk_level_1.txt")

            except Exception as e:
                print(f"{formatted_time} - [ERROR] LONG Recovery failed: {str(e)}")

        # === POSITION SIZE ENHANCEMENT ===

        # Increase Long Position Size when ROE > 8%
        if long == 1 and ROE >= 8.0 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):

            try:
                adjust_long_size = binance_client.futures_create_order(symbol=Coin, side='BUY', type='MARKET', quantity=qty_3)

                message = (f"📈 *{Coin} LONG SIZE ENHANCED*\n\n"
                          f"📊 *Enhancement Details:*\n"
                          f"• ROE: {ROE:.2f}%\n"
                          f"• Additional Size: {qty_3}\n"
                          f"• Trigger: +8% ROE\n\n"
                          f"Position size increased to maximize profits. 🚀")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [SIZE-BOOST] LONG Position enhanced at {ROE}% ROE")

                # Create size adjustment flag
                open(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt", 'w').close()

            except Exception as e:
                print(f"{formatted_time} - [ERROR] LONG Size enhancement failed: {str(e)}")

        # Increase Short Position Size when ROE > 8%
        elif short == 1 and ROE >= 8.0 and not os.path.exists(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt"):

            try:
                adjust_short_size = binance_client.futures_create_order(symbol=Coin, side='SELL', type='MARKET', quantity=qty_3)

                message = (f"📉 *{Coin} SHORT SIZE ENHANCED*\n\n"
                          f"📊 *Enhancement Details:*\n"
                          f"• ROE: {ROE:.2f}%\n"
                          f"• Additional Size: {qty_3}\n"
                          f"• Trigger: +8% ROE\n\n"
                          f"Position size increased to maximize profits. 🚀")
                url = f'https://api.telegram.org/bot{bot_token}/sendMessage?chat_id={chat_id}&text={message}&parse_mode=Markdown'
                requests.get(url)
                print(f"{formatted_time} - [SIZE-BOOST] SHORT Position enhanced at {ROE}% ROE")

                # Create size adjustment flag
                open(f"/root/02_implementation/03_app_logs/{Coin}_size_adjustment.txt", 'w').close()

            except Exception as e:
                print(f"{formatted_time} - [ERROR] SHORT Size enhancement failed: {str(e)}")



# Record the end time
end_time = time.time()
# Calculate the time taken in seconds
execution_time = end_time - start_time
# Convert seconds to minutes
execution_time_minutes = execution_time / 60

# Print the results
print(str(formatted_time) + ' - ' + f"[TIMING] Started: {time.strftime('%H:%M:%S', time.localtime(start_time))}")
print(str(formatted_time) + ' - ' + f"[TIMING] Completed: {time.strftime('%H:%M:%S', time.localtime(end_time))}")
print(str(formatted_time) + ' - ' + f"[TIMING] Duration: {execution_time_minutes:.2f} minutes")
print(str(formatted_time) + ' - ' + "--------------------------------------")

